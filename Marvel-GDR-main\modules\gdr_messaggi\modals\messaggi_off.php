<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

$skip_permissions = true;
include_once __DIR__.'/../../../core.php';

use Models\User;
use Modules\Schede\Scheda;
use Carbon\Carbon;
use Models\Module;

$query = "SELECT DISTINCT IF(gdr_messaggi.idutente=".prepare($user->id).", gdr_messaggi_destinatari.idutente, gdr_messaggi.idutente) AS destinatario, MAX(gdr_messaggi.created_at) AS data, MAX(gdr_messaggi.id) AS idmessaggio FROM gdr_messaggi inner JOIN gdr_messaggi_destinatari ON gdr_messaggi_destinatari.idmessaggio=gdr_messaggi.id WHERE gdr_messaggi.tipo=2 AND ((gdr_messaggi.idutente=".prepare($user->id)." AND gdr_messaggi.deleted_at IS NULL) OR (gdr_messaggi_destinatari.idutente=".prepare($user->id)." AND gdr_messaggi_destinatari.deleted_at IS NULL)) GROUP BY destinatario ORDER BY data DESC";
$conversazioni = $dbo->fetchArray($query);

$messaggio_proprietario = 'idutente';
$idutente = $user->id;

echo '
<style>
    .contacts-list-name{
        color: black;    
    }
</style>';


echo '
<div class="row">
    <div class="col-md-12" style="max-height:1000px;overflow:auto;">
        <ul class="contacts-list">';
        foreach($conversazioni as $conversazione){
            $ultimo_messaggio = $dbo->table('gdr_messaggi')->where('id',$conversazione['idmessaggio'])->first();
            $destinatario = $dbo->table('gdr_messaggi_destinatari')->where('idmessaggio',$ultimo_messaggio->id)->first();

            $immagine = ($ultimo_messaggio->{$messaggio_proprietario}!=$idutente ? $ultimo_messaggio->icona : $destinatario->icona);
            $immagine = ( !empty($immagine) ? $immagine : $rootdir.'/assets/dist/img/user.png');

            $contenuto = preg_replace('/<img[^>]*><br>/', '<i class="fa fa-camera-retro"></i> ', $ultimo_messaggio->contenuto);
            $contenuto = substr(preg_replace('/<img[^>]*><br>/', '<i class="fa fa-camera-retro"></i> ', $ultimo_messaggio->contenuto), 0, 50);

            echo '
            <li>
                <a class="clickable" onclick="caricaConversazione($(\'#conversazione-'.$conversazione['destinatario'].'\'),'.$conversazione['destinatario'].',2,'.($conversazione['numero_personaggio'] ? $conversazione['numero_personaggio'] : 0).','.($conversazione['numero'] ? $conversazione['numero'] : 0).');">
                    <img class="contacts-list-img '.(($ultimo_messaggio->{$messaggio_proprietario}!=$idutente && $destinatario->is_letto==0) ? 'bounce' : '').'" src="'.$immagine.'" >
                    <div class="contacts-list-info">
                        <span class="contacts-list-name">
                            '.($ultimo_messaggio->{$messaggio_proprietario}!=$idutente ? ($ultimo_messaggio->nome_visualizzato ? $ultimo_messaggio->nome_visualizzato : 'Sconosciuto') : ($destinatario->nome_visualizzato ? $destinatario->nome_visualizzato : 'Sconosciuto')).'
                            <small class="contacts-list-date float-right '.(($ultimo_messaggio->{$messaggio_proprietario}!=$idutente && $destinatario->is_letto==0) ? 'bg-success' : '').'" style="padding:4px;" title="'.Carbon::parse($ultimo_messaggio->created_at)->format("d/m/Y H:i").'">
                                '.Carbon::parse($ultimo_messaggio->created_at)->diffForHumans().'
                                <p><button type="button" class="btn btn-xs btn-danger float-right" onclick="rimuoviConversazione('.$conversazione['destinatario'].');"><i class="fa fa-trash"></i></button></p>
                            </small>
                        </span>
                        <span class="contacts-list-msg">'.( ($ultimo_messaggio->{$messaggio_proprietario}==$idutente && $destinatario->is_letto==0) ? '<img src="assets/dist/img/skins/non_letto.svg" style="width:25px;">' : ( ($ultimo_messaggio->{$messaggio_proprietario}==$idutente && $destinatario->is_letto==1) ? '<img src="assets/dist/img/skins/letto.svg" style="width:25px;">' : '')).$contenuto.'</span>
                    </div>
                </a>
                <br>
                <div class="conversazioni_full" id="conversazione-'.$conversazione['destinatario'].'" style="display:none;"></div>
            </li>';
        }
echo'
        </ul>
    </div>
</div>';

?>
<script>
    function caricaConversazione(container,destinatario,tipo,numero,numero_personaggio){
        $(".conversazioni_full").each(function(){
            $(this).hide();
        });

        container.show();
        localLoading(container, true);

        $.get("<?php echo base_path();?>/ajax_complete.php?module=Messaggi&op=conversazione&id_module=<?php echo $id_module;?>&destinatario=" + destinatario + "&tipo=" + tipo + "&numero=" + numero + "&numero_personaggio=" + numero_personaggio, function(data){
            container.html(data);
            localLoading(container, false);
        });
    }
</script>