<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

$skip_permissions = true;
include_once __DIR__.'/../../core.php';

use Modules\Schede\Scheda;

switch (filter('op')) {
    case 'update_settings':

        $sfondo_base64 = null;
        if( !empty(post('sfondo')) ){
            $sfondo_base64 = file_get_contents(post('sfondo'));
            $sfondo_base64 = base64_encode($sfondo_base64);
        }
        
        $impostazioni = $dbo->table('gdr_settings_phone')->where('id_utente',post('id_utente'))->first();
        if( empty($impostazioni) ){
            $dbo->insert('gdr_settings_phone',[
                'id_utente' => post('id_utente'),
                'idsfondo' => post('idsfondo'),
                'sfondo' => post('sfondo'),
                'idaudio' => post('idaudio'),
                'link_audio' => post('link_audio'),
                'sfondo_base64' => $sfondo_base64,
            ]);
        }else{
            $dbo->update('gdr_settings_phone',[
                'id_utente' => post('id_utente'),
                'idsfondo' => post('idsfondo'),
                'sfondo' => post('sfondo'),
                'idaudio' => post('idaudio'),
                'link_audio' => post('link_audio'),
                'sfondo_base64' => ( $sfondo_base64 ? $sfondo_base64 : NULL),
            ],['id' => $impostazioni->id]);
        }

        flash()->info(tr('Impostazioni StarkPhone modificate!'));
        $message = tr('Impostazioni StarkPhone modificate!');

        echo json_encode([
            'result' => true,
            'message' => $message,
        ]);

        break;

    case 'update_rubrica':

        try {
            //controllo se esiste il numero
            $numero = trim(post('numero'));
            //$destinatario = Scheda::where('numero1',$numero)->orWhere('numero2',$numero)->first();
            $destinatario = $dbo->fetchOne("SELECT id FROM gdr_schede WHERE deleted_at IS NULL AND (numero1=".prepare($numero)." OR numero2=".prepare($numero).")")['id'];

            if( !empty($destinatario) && !empty($numero) && strlen($numero)>=9 && !empty(post('numero_personaggio')) ){
                $personaggio = Scheda::find(post('idpersonaggio'));

                if( post('numero_personaggio')==$personaggio->numero1 ){
                    $contatti = (empty($personaggio->rubrica) ? [] : json_decode($personaggio->rubrica,1));
                    $contatti[] = $numero;
    
                    $personaggio->rubrica = json_encode($contatti);
                }elseif( post('numero_personaggio')==$personaggio->numero2 ){
                    $contatti = (empty($personaggio->rubrica2) ? [] : json_decode($personaggio->rubrica2,1));
                    $contatti[] = $numero;

                    $personaggio->rubrica2 = json_encode($contatti);
                }
               
                $personaggio->save();

                flash()->info(tr('Nuovo contatto aggiunto!'));

                $result = true;
                $message = tr('Nuovo contatto aggiunto!');
            }else{
                if( empty(post('numero_personaggio')) ){
                    flash()->error(tr('Selezionare prima la sim in cui salvare il contatto!'));
                    $message = tr('Selezionare prima la sim in cui salvare il contatto!');
                }else{
                    flash()->error(tr('Il numero da lei chiamato è inesistente!'));
                    $message = tr('Il numero da lei chiamato è inesistente!');
                }

                $result = false;
            }
        } catch (InvalidArgumentException) {
            flash()->error(tr('Errore nell\'aggiunta del contatto!'));

            $result = false;
            $message = tr('Errore nell\'aggiunta del contatto!');
        }
        
        echo json_encode([
            'result' => $result,
            'message' => $message,
        ]);
        
        break;

    case 'delete-contatto':

        try {
            $personaggio = Scheda::find(post('idpersonaggio'));

            $contatti = (empty($personaggio->rubrica) ? [] : json_decode($personaggio->rubrica,1));
            $contatti = array_diff($contatti, [post('numero')]);

            $personaggio->rubrica = json_encode($contatti);
            $personaggio->save();

            flash()->info(tr('Contatto rimosso!'));

            $result = true;
            $message = tr('Contatto rimosso!');
        } catch (InvalidArgumentException) {
            flash()->error(tr('Errore nella rimozione del contatto!'));

            $result = false;
            $message = tr('Errore nella rimozione del contatto!');
        }
        
        echo json_encode([
            'result' => $result,
            'message' => $message,
        ]);
        
        break;

    case 'delete-conversazione':

        try {
            $user = Auth::user();
            $mio_idutente = $user->id;
            $mio_idpersonaggio = post('idpersonaggio');
            $iddestinatario = post('iddestinatario');

            // Trova tutti i messaggi della conversazione tra me e il destinatario
            $messaggi_conversazione = $dbo->fetchArray("
                SELECT DISTINCT gdr_messaggi.id
                FROM gdr_messaggi
                INNER JOIN gdr_messaggi_destinatari ON gdr_messaggi_destinatari.idmessaggio = gdr_messaggi.id
                WHERE (
                    (gdr_messaggi.idutente = ".prepare($mio_idutente)." AND gdr_messaggi_destinatari.idutente = ".prepare($iddestinatario).")
                    OR
                    (gdr_messaggi.idutente = ".prepare($iddestinatario)." AND gdr_messaggi_destinatari.idutente = ".prepare($mio_idutente).")
                )
            ");

            if (!empty($messaggi_conversazione)) {
                $ids_messaggi = array_column($messaggi_conversazione, 'id');
                $ids_messaggi_string = implode(',', $ids_messaggi);

                // Imposta deleted_at sui messaggi dove sono il mittente
                $dbo->query("
                    UPDATE gdr_messaggi
                    SET deleted_at = NOW()
                    WHERE id IN (".$ids_messaggi_string.")
                    AND (idutente = ".prepare($mio_idutente)." OR idpersonaggio = ".prepare($mio_idpersonaggio).")
                ");

                // Imposta deleted_at sui destinatari dove sono il destinatario
                $dbo->query("
                    UPDATE gdr_messaggi_destinatari
                    SET deleted_at = NOW()
                    WHERE idmessaggio IN (".$ids_messaggi_string.")
                    AND (idutente = ".prepare($mio_idutente)." OR idpersonaggio = ".prepare($mio_idpersonaggio).")
                ");
            }

            flash()->info(tr('Conversazione rimossa!'));

            $result = true;
            $message = tr('Conversazione rimossa!');
        } catch (InvalidArgumentException) {
            flash()->error(tr('Errore nella rimozione della conversazione!'));

            $result = false;
            $message = tr('Errore nella rimozione della conversazione!');
        }

        echo json_encode([
            'result' => $result,
            'message' => $message,
        ]);

        break;

    case 'add_messaggio':

        $mittente = Scheda::find(post('idpersonaggio_mittente'));
        if( post('numero_personaggio')==$mittente->numero2 ){
            $icona_mittente = $mittente->immagine_contatto_alias;
            $nome_mittente = $mittente->nome2;
            $numero_mittente = $mittente->numero2;
        }else{
            $icona_mittente = $mittente->immagine_contatto;
            $nome_mittente = $mittente->nome1;
            $numero_mittente = $mittente->numero1;
        }

        if( post('tipo')==2 ){
            $nome_mittente = $user->username;
            $icona_mittente = $user->personaggio->immagine_chat;
        }

        $contenuto = (post('immagine') ? '<img src="'.post('immagine').'"><br>' : '').post('contenuto');

        $dbo->insert('gdr_messaggi',[
            'contenuto' => $contenuto,
            'tipo' => post('tipo'),
            'idutente' => post('id_mittente'),
            'idpersonaggio' => post('idpersonaggio_mittente'),
            'icona' => $icona_mittente,
            'nome_visualizzato' => $nome_mittente,
            'numero' => $numero_mittente,
        ]);
        $idmessaggio = $dbo->lastInsertedID();

        //Destinatario
        $dbo->insert('gdr_messaggi_destinatari',[
            'idmessaggio' => $idmessaggio,
            'idutente' => post('id_destinatario'),
            'idpersonaggio' => post('idpersonaggio_destinatario'),
            'icona' => post('icona_destinatario'),
            'nome_visualizzato' => post('nome_destinatario'),
            'numero' => post('numero_destinatario'),
        ]);

        flash()->info(tr('Messaggio inviato!'));

        echo json_encode([
            'result' => true,
            'message' => tr('Messaggio inviato!'),
        ]);

        break;
}
