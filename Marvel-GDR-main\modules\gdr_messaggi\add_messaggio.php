<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */


include_once __DIR__.'/../../core.php';

use Models\User;
use Models\Module;


if( get('op')=='allegato' ){
	echo '
	<div class="row">
		<div class="col-md-12">
			{["type":"text", "label":"'.tr('Immagine').'", "name":"allegato_immagine", "value":"'.get('allegato_immagine').'", "icon-before":"<i class=\"fa fa-camera-retro\"></i>" ]}
		</div>
	</div>

	<div class="row">
		<div class="col-md-12 text-right">
			<button type="button" id="btn-allegato" class="btn btn-primary"><i class="fa fa-send"></i> '.tr('Invia').'</button>
		</div>
	</div>';

	echo '
	<script>
		$("#btn-allegato").click(function(){
			$("#immagine").val( $("#allegato_immagine").val() );
			$(".close:last").trigger("click");
		});
	</script>';
}else{
	$destinatario = User::where('idpersonaggio',get('iddestinatario'))->first();

	echo '
	<form action="" method="post" id="addmessage-form">
		<input type="hidden" name="op" value="add_messaggio">
		<input type="text" name="id_module" value="'..Module::where('name','Messaggi')->first()->id.'">
		<input type="hidden" name="tipo" value="2">
		<input type="hidden" name="id_mittente" value="'.$user->id.'">
		<input type="hidden" name="idpersonaggio_mittente" value="0">
		<input type="hidden" name="id_destinatario" value="'.$destinatario->id.'">
		<input type="hidden" name="idpersonaggio_destinatario" value="0">
		<input type="hidden" name="idpersonaggio_mittente" value="'.$user->idpersonaggio.'">
		<input type="hidden" name="idpersonaggio_destinatario" value="'.get('iddestinatario').'">
		<input type="hidden" name="backto" value="record-edit">
		<input type="hidden" name="icona_destinatario" value="'.$destinatario->personaggio->immagine_chat.'">
		<input type="hidden" name="nome_destinatario" value="'.$destinatario->username.'">

		<div class="row">
			<div class="col-md-12">
				{[ "type":"text", "label":"'.tr('Immagine').'", "name":"immagine", "value":"", "icon-before":"<i class=\"fa fa-camera-retro\"></i>" ]}
			</div>
		</div>
		<div class="row">
			<div class="col-md-12">
				'.input([
					'type' => 'ckeditor',
					'label' => tr('Messaggio'),
					'name' => 'contenuto',
					'required' => 0,
					'value' => "",
					'extra' => 'style=\'max-height:40px;\'',
				]).'
			</div>
		</div>

		<div class="row">
			<div class="col-md-12 text-right">
				<a class="btn btn-primary" onclick="sendMessage();"><i class="fa fa-send"></i> '.tr('Invia').'</a>
			</div>
		</div>
	</form>';

	echo '
	<script>
		function sendMessage(){
			$.ajax({
				url: globals.rootdir + "/actions.php",
				type: "POST",
				dataType: "json",
				data: $("#addmessage-form").serialize(),
				success: function (response) {
					renderMessages();
					$(".close").last().trigger("click");
				},
				error: function() {
					swal("'.tr('Errore').'", "'.tr('Errore nel salvataggio delle informazioni').'", "error");
				}
			});
		}
	</script>';
}