<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

include_once __DIR__.'/../../../core.php';

use Models\User;
use Modules\Schede\Scheda;
use Carbon\Carbon;
use Models\Module;

Carbon::setLocale('it');

switch ($resource) {

    case 'rubrica':

        global $rootdir;

        $personaggio = Scheda::find(get('idpersonaggio'));

        $numero_sim = [
            '\"'.$personaggio->numero1.'\":\"'.$personaggio->numero1.' <small>(Privato)</small>\"',
            '\"'.$personaggio->numero2.'\":\"'.$personaggio->numero2.' <small>(Alias)</small>\"',
        ];

        echo '
        <div class="row">
            <div class="col-md-4">
                {["type":"select", "label":"'.tr('SIM').'", "name":"numero_personaggio", "values":"list='.implode(",",$numero_sim).'", "value":"" ]}
            </div>
            <div class="col-md-4">
                {["type":"text", "label":"'.tr('Numero').'", "name":"numero", "value":"" ]}
            </div>
            <div class="col-md-4 div-button ">
                <a type="button" class="btn btn-primary" onclick="aggiungiContatto();"><i class="fa fa-plus"></i> '.tr('Aggiungi').'</a>
            </div>
        </div>';

        $contatti = (empty($personaggio->rubrica) ? [] : json_decode($personaggio->rubrica,1));
        $contatti2 = (empty($personaggio->rubrica2) ? [] : json_decode($personaggio->rubrica2,1));

    echo '
        <hr>';
    if( empty($personaggio->numero1) && empty($personaggio->numero2) ){
        echo '
        <div class="alert alert-warning text-center">
            <i class="fa fa-warning"></i> '.tr('Nessun numero di telefono impostato per il personaggio _NOME_',[
                '_NOME_' => $personaggio->nome." ".$personaggio->cognome,
            ]).'
        </div>';
    }

    if( !empty($personaggio->numero1) ){
        echo '
        <div class="row">
            <div class="col-md-12">
                <div class="card card-outline card-warning collapsable collapsed-card" style="background-color: transparent;">
                    <div class="card-header with-border">
                        <h3 class="card-title" id="accessi-title"><i class="fa fa-book nav-icon"></i> '.tr('Rubrica SIM1').' <small class="text-muted">('.$personaggio->numero1.')</small></h3>
                        <div class="card-tools pull-right">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse"><i class="fa fa-plus"></i></button>
                        </div>
                    </div>
                    <div class="card-body" id="card-access">
                        <div id="div-access" style="max-height: 500px;overflow:auto;"></div>
                            <table class="table table-bordered table-messaggi">';

                            foreach($contatti as $contatto){
                                $destinatario = Scheda::where('numero1',$contatto)->orWhere('numero2',$contatto)->first();

                                if( $destinatario ){
                                    if($contatto==$destinatario->numero1){
                                        $nome = $destinatario->nome1;
                                        $immagine = $destinatario->immagine_contatto;
                                    }else{
                                        $nome = $destinatario->nome2;
                                        $immagine = $destinatario->immagine_contatto_alias;
                                    }

                                    echo '
                                    <tr class="clickable" onclick="caricaConversazione($(\'#div-telefono\'),'.$destinatario->id.',1,'.$contatto.','.$personaggio->numero1.');">
                                        <td width="15%">';
                                        if( !empty($immagine) ){
                                            echo '
                                            <img src="'.$immagine.'" class="contacts-list-img" style="width: 100%;" />';
                                        }else{
                                            echo '
                                            <img src="'.$rootdir.'/assets/dist/img/user.png" class="contacts-list-img" style="width: 100%;" />';
                                        }
                                    echo '
                                        </td>
                                        <td>
                                            '.$nome.'
                                            <br>
                                            <small class="text-muted">'.$contatto.'</small>
                                        </td>
                                        <td width="5%">
                                            <a class="btn btn-xs btn-danger" onclick="rimuovi(\''.$contatto.'\');"><i class="fa fa-trash"></i></a>
                                        </td>
                                    </tr>';
                                }
                            }
        echo '
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }
    if( !empty($personaggio->numero2) ){
        echo '
        <div class="row">
            <div class="col-md-12">
                <div class="card card-outline card-warning collapsable collapsed-card" style="background-color: transparent;">
                    <div class="card-header with-border">
                        <h3 class="card-title" id="accessi-title"><i class="fa fa-book nav-icon"></i> '.tr('Rubrica SIM2').' <small class="text-muted">('.$personaggio->numero2.')</small></h3>
                        <div class="card-tools pull-right">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse"><i class="fa fa-plus"></i></button>
                        </div>
                    </div>
                    <div class="card-body" id="card-access">
                        <div id="div-access" style="max-height: 500px;overflow:auto;"></div>
                            <table class="table table-bordered table-messaggi">';

                            foreach($contatti2 as $contatto){
                                $destinatario = Scheda::where('numero1',$contatto)->orWhere('numero2',$contatto)->first();

                                if( $destinatario ){
                                    if($contatto==$destinatario->numero1){
                                        $nome = $destinatario->nome1;
                                        $immagine = $destinatario->immagine_contatto;
                                    }else{
                                        $nome = $destinatario->nome2;
                                        $immagine = $destinatario->immagine_contatto_alias;
                                    }

                                    echo '
                                    <tr class="clickable" onclick="caricaConversazione($(\'#div-telefono\'),'.$destinatario->id.',1,'.$contatto.','.$personaggio->numero2.');">
                                        <td width="15%">';
                                        if( !empty($immagine) ){
                                            echo '
                                            <img src="'.$immagine.'" class="contacts-list-img" style="width: 100%;" />';
                                        }else{
                                            echo '
                                            <img src="'.$rootdir.'/assets/dist/img/user.png" class="contacts-list-img" style="width: 100%;" />';
                                        }
                                    echo '
                                        </td>
                                        <td>
                                            '.$nome.'
                                            <br>
                                            <small class="text-muted">'.$contatto.'</small>
                                        </td>
                                        <td width="5%">
                                            <a class="btn btn-xs btn-danger" onclick="rimuovi(\''.$contatto.'\');"><i class="fa fa-trash"></i></a>
                                        </td>
                                    </tr>';
                                }
                            }

        echo '
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }

        echo '
        <script>
            function aggiungiContatto(){
                $.ajax({
                    url: globals.rootdir + "/actions.php",
                    cache: false,
                    type: "POST",
                    dataType: "JSON",
                    data: {
                        op: "update_rubrica",
                        id_module: '.Module::where('name','Messaggi')->first()->id.',
                        idpersonaggio: '.get('idpersonaggio').',
                        numero_personaggio: $("#numero_personaggio").val(),
                        numero: $("#numero").val(),
                    },
                    success: function(data) {
                        renderMessages();
                        caricaHome(\'rubrica\');
                    },
                    error: function(data) {
                        swal("'.tr('Errore').'", "'.tr('Errore durante il salvataggio dei dati').'", "error");
                    }
                });
            }

            function rimuovi(numero){
                swal({
                    title: "'.tr('Rimuovere questo contatto?').'",
                    html: "'.tr('Sei sicuro di volere rimuovere questo contatto?').' '.tr("L'operazione è irreversibile").'.",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonText: "'.tr('Sì').'"
                }).then(function () {
                    $.ajax({
                        url: globals.rootdir + "/actions.php",
                        type: "POST",
                        dataType: "json",
                        data: {
                            id_module: '.Module::where('name','Messaggi')->first()->id.',
                            op: "delete-contatto",
                            idpersonaggio: '.get('idpersonaggio').',
                            numero: numero,
                        },
                        success: function (response) {
                            renderMessages();
                            caricaHome(\'rubrica\');
                        },
                        error: function() {
                            swal("'.tr('Errore').'", "'.tr('Errore nel salvataggio delle informazioni').'", "error");
                        }
                    });
                }).catch(swal.noop);
            }
        </script>';

        break;

    case 'settings':
        $impostazioni = $dbo->table('gdr_settings_phone')->where('id_utente',get('id_utente'))->first();

        echo'
        <div class="alert alert-info">'.tr('Questa schermata è ottimizzata per le dimensioni degli sfondi per smartphone').'</div>';

        echo '
        <form action="" method="post" id="settings-form" enctype="multipart/form-data">
            <fieldset>
                <input type="hidden" name="backto" value="record-edit">
                <input type="hidden" name="op" value="update_settings">
                <input type="hidden" name="id_module" value="'.Module::where('name','Messaggi')->first()->id.'">
                <input type="hidden" name="id_utente" value="'.get('id_utente').'">

                <div class="row">
                    <div class="col-md-6">
                        {["type":"select", "label":"'.tr('Sfondo preimpostato').'", "name":"idsfondo", "values":"list=\"standard\":\"'.tr('Classic').'\", \"avengers\":\"'.tr('Avengers').'\", \"fantastic\":\"'.tr('Fantastici 4').'\", \"xmen\":\"'.tr('X-Men').'\" ", "value":"'.($impostazioni->idsfondo ? $impostazioni->idsfondo : 'standard').'" ]}
                    </div>
                    <div class="col-md-6">
                        {[ "type": "text", "label": "'.tr('Sfondo personalizzato').'", "name": "sfondo", "value": "'.$impostazioni->sfondo.'" ]}
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        {["type":"select", "label":"'.tr('Suoneria preimpostata').'", "name":"idaudio", "values":"list=\"'.$rootdir.'/assets/dist/img/skins/media/xmen_theme.mp3\":\"'.tr('X-Men').'\", \"'.$rootdir.'/assets/dist/img/skins/media/spidermansound.mp3\":\"'.tr('Spiderman').'\", \"'.$rootdir.'/assets/dist/img/skins/media/avengers.mp3\":\"'.tr('Avengers').'\", \"'.$rootdir.'/assets/dist/img/skins/media/jarvis.mp3\":\"'.tr('JARVIS').'\" ", "value":"'.($impostazioni->idaudio ? $impostazioni->idaudio : '').'" ]}
                    </div>
                    <div class="col-md-6">
                        {[ "type": "text", "label": "'.tr('Suoneria personalizzata').'", "name": "link_audio", "value": "'.$impostazioni->link_audio.'" ]}
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 text-right">
                        <a onclick="salvaSettings();" class="btn btn-success"><i class="fa fa-save"></i> '.tr('Salva').'</a>
                    </div>
                </div>
            </fieldset>
        </form>

        <script>
            function salvaSettings(){
                $.ajax({
                    url: globals.rootdir + "/actions.php",
                    type: "POST",
                    dataType: "json",
                    data: $("#settings-form").serialize(),
                    success: function (response) {
                        renderMessages();
                        caricaHome(\'home_telefono\');
                    },
                    error: function() {
                        swal("'.tr('Errore').'", "'.tr('Errore nel salvataggio delle informazioni').'", "error");
                    }
                });
            }
        </script>';

        break;

    case 'orario':

        $now = Carbon::now();

        echo '
        <h1>'.$now->format("H:i").'</h1>
        <p>'.$now->locale('it')->isoFormat('ddd, D MMMM').'</p>';

        break;

    case 'home_telefono':
        $user = Auth::user();

        echo '
        <div class="row">
            <div class="col-md-6" id="data-ora"></div>
        </div>';

        // Controllo se ci sono messaggi non letti di tipo 1 (ON)
        $query = "SELECT gdr_messaggi_destinatari.id FROM gdr_messaggi_destinatari INNER JOIN gdr_messaggi ON gdr_messaggi.id=gdr_messaggi_destinatari.idmessaggio WHERE gdr_messaggi_destinatari.is_letto=0 AND (gdr_messaggi_destinatari.idutente=".prepare($user->id)." OR gdr_messaggi_destinatari.idpersonaggio=".prepare($user->personaggio->id).") AND gdr_messaggi.tipo=1";
        $messaggi = $dbo->fetchArray($query);
        $count_messaggi = count($messaggi);

        // Classe e attributo per il badge di notifica
        $badge_class = $count_messaggi > 0 ? '' : 'hidden';

        $news = $dbo->table('gdr_news')
            ->join('gdr_news_user','gdr_news_user.idnews','gdr_news.id')
            ->select('gdr_news.id','gdr_news.testata')
            ->where('gdr_news_user.idutente',$user->id)
            ->get();

        $count_buggle = 0;
        $count_buletin = 0;
        $count_blog = 0;

        foreach($news as $notizia){
            if( $notizia->testata==1 ){
                ++$count_buggle;
            }elseif( $notizia->testata==2 ){
                ++$count_buletin;
            }else{
                ++$count_blog;
            }
        }

        $badge_buggle = $count_buggle > 0 ? '' : 'hidden';
        $badge_buletin = $count_buletin > 0 ? '' : 'hidden';
        $badge_blog = $count_blog > 0 ? '' : 'hidden';

        //menu telefono
        echo '
        <div class="row">
            <div class="app messaggistica" onclick="caricaLista($(\'#div-telefono\'));">
                <div class="badge badge-danger pull-right '.$badge_class.'">'.$count_messaggi.'</div>
            </div>
            <div class="app rubrica" onclick="caricaHome(\'rubrica\');" ></div>
            <div class="app settings" onclick="caricaHome(\'settings\');" ></div>
        </div>
        <div class="row">
            <div class="app daily_buggle" onclick="caricaHome(\'daily_buggle\');" >
                <div class="badge badge-danger pull-right '.$badge_buggle.'">'.$count_buggle.'</div>
            </div>
            <div class="app buletin" onclick="caricaHome(\'buletin\');" >
                <div class="badge badge-danger pull-right '.$badge_buletin.'">'.$count_buletin.'</div>
            </div>
            <div class="app blog" onclick="caricaHome(\'blog\');" >
                <div class="badge badge-danger pull-right '.$badge_blog.'">'.$count_blog.'</div>
            </div>
        </div>';

        echo '
        <script>
            $(document).ready(function(){
                $(".app").each(function(){
                    $(this).height($(this).width());
                });

                getDataOra();
                setInterval(function(){
                    getDataOra();
                },60000);
            });

            function getDataOra(){
                var container = $("#data-ora");
                localLoading(container, true);

                $.get("'.base_path().'/ajax_complete.php?module=Messaggi&op=orario", function(data){
                    container.html(data);
                    localLoading(container, false);
                });
            }
        </script>';

        break;

    case 'lista_conversazioni':

        global $rootdir;
        $user = Auth::user();

        //messaggi OFF
        if( get('tipo')==2 ){
            $conversazioni = $dbo->fetchArray("SELECT DISTINCT IF(gdr_messaggi.idutente=".prepare($user->id).", gdr_messaggi_destinatari.idutente, gdr_messaggi.idutente) AS destinatario, MAX(gdr_messaggi.created_at) AS data, MAX(gdr_messaggi.id) AS idmessaggio FROM gdr_messaggi LEFT JOIN gdr_messaggi_destinatari ON gdr_messaggi_destinatari.idmessaggio=gdr_messaggi.id WHERE gdr_messaggi.tipo=2 AND (gdr_messaggi.idutente=".prepare($user->id)." OR gdr_messaggi_destinatari.idutente=".prepare($user->id).") AND gdr_messaggi_destinatari.deleted_at IS NULL GROUP BY destinatario ORDER BY data DESC");

            $messaggio_proprietario = 'idutente';
            $idutente = $user->id;
        }else{
            $conversazioni = $dbo->fetchArray("SELECT DISTINCT IF(gdr_messaggi.idpersonaggio=".prepare($user->personaggio->id).", gdr_messaggi_destinatari.idpersonaggio, gdr_messaggi.idpersonaggio) AS destinatario, IF(gdr_messaggi.idpersonaggio=".prepare($user->personaggio->id).", gdr_messaggi_destinatari.numero, gdr_messaggi.numero) AS numero_personaggio, IF(gdr_messaggi.idpersonaggio!=".prepare($user->personaggio->id).", gdr_messaggi_destinatari.numero, gdr_messaggi.numero) AS numero, MAX(gdr_messaggi.created_at) AS data, MAX(gdr_messaggi.id) AS idmessaggio FROM gdr_messaggi LEFT JOIN gdr_messaggi_destinatari ON gdr_messaggi_destinatari.idmessaggio=gdr_messaggi.id WHERE gdr_messaggi.tipo=1 AND (gdr_messaggi.idpersonaggio=".prepare($user->personaggio->id)." OR gdr_messaggi_destinatari.idpersonaggio=".prepare($user->personaggio->id).") AND gdr_messaggi_destinatari.deleted_at IS NULL GROUP BY destinatario, numero_personaggio, numero ORDER BY data DESC");
            $messaggio_proprietario = 'idpersonaggio';
            $idutente = $user->personaggio->id;
        }

        echo '
        <div class="row">
            <div class="direct-chat-contacts" style="transform: translate(1%,0);">
                <ul class="contacts-list">';
                foreach($conversazioni as $conversazione){
                    $ultimo_messaggio = $dbo->table('gdr_messaggi')->where('id',$conversazione['idmessaggio'])->first();
                    $destinatario = $dbo->table('gdr_messaggi_destinatari')->where('idmessaggio',$ultimo_messaggio->id)->first();

                    $immagine = ($ultimo_messaggio->{$messaggio_proprietario}!=$idutente ? $ultimo_messaggio->icona : $destinatario->icona);
                    $immagine = ( !empty($immagine) ? $immagine : $rootdir.'/assets/dist/img/user.png');

                    $contenuto = substr(preg_replace('/<img[^>]*><br>/', '<i class="fa fa-camera-retro"></i> ', $ultimo_messaggio->contenuto), 0, 50);

                    echo '
                    <li>
                        <a class="clickable" onclick="caricaConversazione($(\'#div-telefono\'),'.$conversazione['destinatario'].','.get('tipo').','.($conversazione['numero_personaggio'] ? $conversazione['numero_personaggio'] : 0).','.($conversazione['numero'] ? $conversazione['numero'] : 0).');">
                            <img class="contacts-list-img '.(($ultimo_messaggio->{$messaggio_proprietario}!=$idutente && $destinatario->is_letto==0) ? 'bounce' : '').'" src="'.$immagine.'" >
                            <div class="contacts-list-info">
                                <span class="contacts-list-name">
                                    '.($ultimo_messaggio->{$messaggio_proprietario}!=$idutente ? ($ultimo_messaggio->nome_visualizzato ? $ultimo_messaggio->nome_visualizzato : 'Sconosciuto') : ($destinatario->nome_visualizzato ? $destinatario->nome_visualizzato : 'Sconosciuto')).'
                                    <small class="contacts-list-date float-right '.(($ultimo_messaggio->{$messaggio_proprietario}!=$idutente && $destinatario->is_letto==0) ? 'bg-success' : '').'" style="padding:4px;" title="'.Carbon::parse($ultimo_messaggio->created_at)->format("d/m/Y H:i").'">
                                        '.Carbon::parse($ultimo_messaggio->created_at)->diffForHumans().'
                                        <p><button type="button" class="btn btn-xs btn-danger float-right" onclick="rimuoviConversazione('.$conversazione['destinatario'].');"><i class="fa fa-trash"></i></button></p>
                                    </small>
                                </span>
                                <span class="contacts-list-msg">'.( ($ultimo_messaggio->{$messaggio_proprietario}==$idutente && $destinatario->is_letto==0) ? '<img src="assets/dist/img/skins/non_letto.svg" style="width:25px;">' : ( ($ultimo_messaggio->{$messaggio_proprietario}==$idutente && $destinatario->is_letto==1) ? '<img src="assets/dist/img/skins/letto.svg" style="width:25px;">' : '')).$contenuto.'</span>
                            </div>
                        </a>
                    </li>';
                }
        echo'
                </ul>
            </div>
        </div>';

        echo '
        <script>
            function rimuoviConversazione(iddestinatario){
                swal({
                    title: "'.tr('Rimuovere questa conversazione?').'",
                    html: "'.tr('Sei sicuro di volere rimuovere questa conversazione?').' '.tr("L'operazione è irreversibile").'.",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonText: "'.tr('Sì').'"
                }).then(function () {
                    $.ajax({
                        url: globals.rootdir + "/actions.php",
                        type: "POST",
                        dataType: "json",
                        data: {
                            id_module: '.Module::where('name','Messaggi')->first()->id.',
                            op: "delete-conversazione",
                            idpersonaggio: '.get('idpersonaggio').',
                            iddestinatario: iddestinatario,
                        },
                        success: function (response) {
                            renderMessages();
                            caricaLista($("#div-telefono"));
                        },
                        error: function() {
                            swal("'.tr('Errore').'", "'.tr('Errore nel salvataggio delle informazioni').'", "error");
                        }
                    });
                }).catch(swal.noop);
            }
        </script>';

        break;

    case 'conversazione':
        $user = Auth::user();

        if( get('tipo')==2 ){
            $messaggi = $dbo->fetchArray("SELECT DISTINCT gdr_messaggi.*, gdr_messaggi_destinatari.idmessaggio FROM gdr_messaggi INNER JOIN gdr_messaggi_destinatari ON gdr_messaggi_destinatari.idmessaggio=gdr_messaggi.id WHERE tipo=2 AND (gdr_messaggi_destinatari.idutente=".prepare($user->id)." OR gdr_messaggi.idutente=".prepare($user->id).") AND (gdr_messaggi_destinatari.idutente=".prepare(get('destinatario'))." OR gdr_messaggi.idutente=".prepare(get('destinatario')).") ORDER BY gdr_messaggi.created_at DESC");

            $destinatario = User::find(get('destinatario'));
            $icona_destinatario = $destinatario->personaggio->immagine_chat;
            $nome_destinatario = $destinatario->username;
        }else{
            $messaggi = $dbo->fetchArray("SELECT DISTINCT gdr_messaggi.*, gdr_messaggi_destinatari.idmessaggio FROM gdr_messaggi INNER JOIN gdr_messaggi_destinatari ON gdr_messaggi_destinatari.idmessaggio=gdr_messaggi.id WHERE tipo=1 AND (gdr_messaggi_destinatari.idpersonaggio=".prepare($user->personaggio->id)." OR gdr_messaggi.idpersonaggio=".prepare($user->personaggio->id).") AND (gdr_messaggi_destinatari.idpersonaggio=".prepare(get('destinatario'))." OR gdr_messaggi.idpersonaggio=".prepare(get('destinatario')).") AND (gdr_messaggi_destinatari.numero=".prepare(get('numero'))." OR gdr_messaggi.numero=".prepare(get('numero')).") AND (gdr_messaggi_destinatari.numero=".prepare(get('numero_personaggio'))." OR gdr_messaggi.numero=".prepare(get('numero_personaggio')).") ORDER BY gdr_messaggi.created_at DESC");

            $destinatario = Scheda::find(get('destinatario'));
            if( get('numero')==$destinatario->numero1 ){
                $icona_destinatario = $destinatario->immagine_contatto;
                $nome_destinatario = $destinatario->nome1;
            }else{
                $icona_destinatario = $destinatario->immagine_contatto2;
                $nome_destinatario = $destinatario->nome2;
            }
        }

        echo '
        <div class="card direct-chat direct-chat-primary" style="height: 100%;overflow:auto;">
            <div class="card-body">
                <div class="direct-chat-messages">';
                    foreach($messaggi as $messaggio){
                        //segno la conversazione come letta per me
                        if( get('tipo')==2 ){
                            $dbo->update('gdr_messaggi_destinatari',[
                                'is_letto' => 1,
                            ],[
                                'idutente' => $user->id,
                                'idmessaggio' => $messaggio['idmessaggio'],
                            ]);
                        }else{
                            $dbo->update('gdr_messaggi_destinatari',[
                                'is_letto' => 1,
                            ],[
                                'numero' => get('numero_personaggio'),
                                'idmessaggio' => $messaggio['idmessaggio'],
                            ]);
                        }

                        $class = ($messaggio['idutente']!=$user->id ? 'left' : 'right');

                        $nome = ($messaggio['nome_visualizzato'] ? $messaggio['nome_visualizzato'] : 'Sconosciuto');
                        $icona = ($messaggio['icona'] ? $messaggio['icona'] : $rootdir.'/assets/dist/img/user.png');

                        echo '
                        <div class="direct-chat-msg '.$class.'">
                            <div class="direct-chat-infos clearfix">
                                <span class="direct-chat-name float-'.$class.'">'.$nome.'</span>
                                <span class="direct-chat-timestamp float-'.($class=='left' ? 'right' : 'left').'">'.Carbon::parse($messaggio['created_at'])->format('d/m/Y H:i').'</span>
                            </div>
                            <img class="direct-chat-img" src="'.$icona.'">
                            <div class="direct-chat-text">'.$messaggio['contenuto'].'</div>
                        </div>';
                    }
            echo '
                    </div>
                </div>
                <div class="card-footer">
                    <form action="" method="post" id="message_form">
                        <input type="hidden" name="op" value="add_messaggio">
                        <input type="hidden" name="tipo" value="'.get('tipo').'">
		                <input type="hidden" name="id_module" value="'.Module::where('name','Messaggi')->first()->id.'">
                        <input type="hidden" name="numero_personaggio" value="'.get('numero_personaggio').'">
                        <input type="hidden" name="numero_destinatario" value="'.get('numero').'">
                        <input type="hidden" name="id_mittente" value="'.$user->id.'">
                        <input type="hidden" name="idpersonaggio_mittente" value="'.$user->personaggio->id.'">';
                    if( get('tipo')==1 ){
                        echo '
                        <input type="hidden" name="idpersonaggio_destinatario" value="'.get('destinatario').'">';
                    }else{
                        echo '
                        <input type="hidden" name="id_destinatario" value="'.get('destinatario').'">';
                    }
            echo '
                        <input type="hidden" name="icona_destinatario" value="'.$icona_destinatario.'">
                        <input type="hidden" name="nome_destinatario" value="'.$nome_destinatario.'">
                        <input type="hidden" name="immagine" id="immagine" value="">

                        <div class="input-group">
                            <span class="input-group-append">
                                <a type="button" onclick="launch_modal(\''.tr('Immagine').'\', \''.base_path().'/modules/gdr_messaggi/add_messaggio.php?id_module='.Module::where('name','Messaggi')->first()->id.'&op=allegato&allegato_immagine=\' + $(\'#immagine\').val());" class="btn btn-default"><i class="fa fa-camera-retro"></i></a>
                            </span>
                            <input type="text" name="contenuto" placeholder="'.tr('Scrivi un messaggio').'..." class="form-control" required>
                            <span class="input-group-append">
                                <a type="button" onclick="inviaMessaggio();" class="btn btn-primary">'.tr('Invia').'</a>
                            </span>
                        </div>
                    </form>
                </div>
            </div>
        </div>';

        echo '
        <script>
            $(document).ready(function() {
                controlloMessaggi();
            });
            function inviaMessaggio(){
                var form = $("#message_form");
                if (form.parsley().validate() && confirm("Inviare il messaggio?")) {
                    $.ajax({
                    url: globals.rootdir + "/actions.php",
                    data: form.serialize(),
                    type: "POST",
                    dataType: "json",
                    success: function (response) {
                        renderMessages();
                        setTimeout(function(){
                            caricaConversazione($(\'#div-telefono\'),'.get('destinatario').','.get('tipo').','.get('numero').');
                        },100);
                    },
                    error: function() {
                        renderMessages();
                    }
                });
                }
            }
        </script>';

        break;

    case 'check_messagi':
        $user = Auth::user();

        $results = [];
        $news = [];

        //controllo se ci sono messaggi da leggere
        $query = "SELECT gdr_messaggi_destinatari.id FROM gdr_messaggi_destinatari INNER JOIN gdr_messaggi ON gdr_messaggi.id=gdr_messaggi_destinatari.idmessaggio WHERE gdr_messaggi_destinatari.is_letto=0 AND (gdr_messaggi_destinatari.idutente=".prepare($user->id)." OR gdr_messaggi_destinatari.idpersonaggio=".prepare($user->personaggio->id).") AND gdr_messaggi.tipo=".prepare(get('tipo'));
        $messaggi = $dbo->fetchArray($query);

        if( get('tipo')==1 ){
            //controllo se ci sono news da leggere
            $query = "SELECT id FROM gdr_news_user WHERE idutente=".prepare($user->id);
            $news = $dbo->fetchArray($query);
        }

        $results = array_merge($messaggi, $news);

        echo count($results);

        break;

    case 'daily_buggle':
        $user = Auth::user();

        echo '
        <style>
            .h-100 {
                height: auto !important;
            }
        </style>';

        echo '<div class="row"><div class="col-md-12 text-center" style="background-image:url(\'/assets/dist/img/skins/messaggi/buggle.png\'); background-size: 100%; background-repeat: no-repeat; height:8vh; color: white; padding: 10px; margin-bottom: 0;"></div></div>';

        $news = $dbo->fetchArray("SELECT id, titolo, data FROM gdr_news WHERE testata=1 AND is_visible=1 ORDER BY data DESC");
        if( !empty($news) ){
            $dbo->query("DELETE FROM gdr_news_user WHERE idutente=".prepare($user->id)." AND idnews IN (".implode(",",array_column($news,'id')).")");
        }

        if (count($news) > 0) {
            echo '<div class="row"><div class="col-md-12" style="padding:0;">';
            foreach ($news as $article) {
                echo '<div class="news-item card shadow-sm h-100" id="article-'.$article['id'].'" style="height:auto;">
                    <div class="card-body" style="background-color: white; color: black;">
                        <h5 class="card-title" style="margin-top: 0;">'.$article['titolo'].'</h5>
                        <div class="article-content" id="content-'.$article['id'].'" style="display: none; background-color: white; color: black;"></div>
                    </div>
                    <div class="card-footer py-3" style="background-color: #0066cc; color: white; padding: 8px; display: flex; justify-content: space-between;">
                        <span><i class="fa fa-calendar"></i> '.Carbon::parse($article['data'])->format('d/m/Y').'</span>
                        <span class="read-more">
                            <a class="pull-right" href="javascript:void(0)" onclick="caricaArticolo('.$article['id'].')" style="color: white; text-decoration: none; font-weight: bold;" id="read-more-'.$article['id'].'">READ MORE</a>
                        </span>
                    </div>
                </div>';
            }
            echo '</div></div>';

            echo '<script>
                function caricaArticolo(id) {
                    var contentDiv = $("#content-" + id);
                    var readMoreBtn = $("#read-more-" + id);

                    // If content is already loaded and visible, hide it
                    if (contentDiv.is(":visible") && contentDiv.html() !== "") {
                        contentDiv.slideUp(300);
                        readMoreBtn.text("READ MORE");
                        return;
                    }

                    // If content is already loaded but hidden, show it
                    if (contentDiv.html() !== "") {
                        contentDiv.slideDown(300);
                        readMoreBtn.text("CLOSE");
                        return;
                    }

                    // Otherwise, load the content
                    localLoading(contentDiv, true);

                    $.get("'.base_path().'/ajax_complete.php?module=Messaggi&op=articolo&id=" + id, function(data) {
                        contentDiv.html(data);
                        contentDiv.slideDown(300);
                        readMoreBtn.text("CLOSE");
                        localLoading(contentDiv, false);
                    });
                }
            </script>';
        } else {
            echo '<div class="alert alert-info" style="background-color: transparent; color: white; border: 1px solid rgba(255, 255, 255, 0.3);">Non ci sono notizie disponibili al momento.</div>';
        }
        break;

    case 'buletin':
        $user = Auth::user();

        echo '
        <style>
            .h-100 {
                height: auto !important;
            }
        </style>';

        echo '<div class="row"><div class="col-md-12 text-center" style="background-image:url(\'/assets/dist/img/skins/messaggi/bulletin.png\'); background-size: 100%; background-repeat: no-repeat; height:7vh; color: white; padding: 10px; margin-bottom: 0;"></div></div>';

        $news = $dbo->fetchArray("SELECT id, titolo, data FROM gdr_news WHERE testata=2 AND is_visible=1 ORDER BY data DESC");
        if( !empty($news) ){
            $dbo->query("DELETE FROM gdr_news_user WHERE idutente=".prepare($user->id)." AND idnews IN (".implode(",",array_column($news,'id')).")");
        }

        if (count($news) > 0) {
            echo '<div class="row"><div class="col-md-12" style="padding:0;">';
            foreach ($news as $article) {
                echo '<div class="news-item card shadow-sm h-100" id="article-'.$article['id'].'" style="height:auto;">
                    <div class="card-body" style="background-color: white; color: black;">
                        <h5 class="card-title" style="margin-top: 0;">'.$article['titolo'].'</h5>
                        <div class="article-content" id="content-'.$article['id'].'" style="display: none; background-color: white; color: black;"></div>
                    </div>
                    <div class="card-footer py-3" style="background-color: #0066cc; color: white; padding: 8px; display: flex; justify-content: space-between;">
                        <span><i class="fa fa-calendar"></i> '.Carbon::parse($article['data'])->format('d/m/Y').'</span>
                        <span class="read-more">
                            <a href="javascript:void(0)" onclick="caricaArticolo('.$article['id'].')" style="color: white; text-decoration: none; font-weight: bold;" id="read-more-'.$article['id'].'">READ MORE</a>
                        </span>
                    </div>
                </div>';
            }
            echo '</div></div>';

            echo '<script>
                function caricaArticolo(id) {
                    var contentDiv = $("#content-" + id);
                    var readMoreBtn = $("#read-more-" + id);

                    // If content is already loaded and visible, hide it
                    if (contentDiv.is(":visible") && contentDiv.html() !== "") {
                        contentDiv.slideUp(300);
                        readMoreBtn.text("READ MORE");
                        return;
                    }

                    // If content is already loaded but hidden, show it
                    if (contentDiv.html() !== "") {
                        contentDiv.slideDown(300);
                        readMoreBtn.text("CLOSE");
                        return;
                    }

                    // Otherwise, load the content
                    localLoading(contentDiv, true);

                    $.get("'.base_path().'/ajax_complete.php?module=Messaggi&op=articolo&id=" + id, function(data) {
                        contentDiv.html(data);
                        contentDiv.slideDown(300);
                        readMoreBtn.text("CLOSE");
                        localLoading(contentDiv, false);
                    });
                }
            </script>';
        } else {
            echo '<div class="alert alert-info" style="background-color: transparent; color: white; border: 1px solid rgba(255, 255, 255, 0.3);">Non ci sono notizie disponibili al momento.</div>';
        }
        break;

    case 'blog':
        $user = Auth::user();

        echo '
        <style>
            .h-100 {
                height: auto !important;
            }
        </style>';

        echo '<div class="row"><div class="col-md-12 text-center" style="background-image:url(\'/assets/dist/img/skins/messaggi/blog.jpg\'); background-size: 100%; background-position:center; background-repeat: no-repeat; height:7vh; color: white; padding: 10px; margin-bottom: 0;"></div></div>';

        $news = $dbo->fetchArray("SELECT id, titolo, data FROM gdr_news WHERE testata=3 AND is_visible=1 ORDER BY data DESC");
        if( !empty($news) ){
            $dbo->query("DELETE FROM gdr_news_user WHERE idutente=".prepare($user->id)." AND idnews IN (".implode(",",array_column($news,'id')).")");
        }

        if (count($news) > 0) {
            echo '<div class="row"><div class="col-md-12" style="padding:0;">';
            foreach ($news as $article) {
                echo '<div class="news-item card shadow-sm h-100" id="article-'.$article['id'].'" style="height:auto;">
                    <div class="card-body" style="background-color: white; color: black;">
                        <h5 class="card-title" style="margin-top: 0;">'.$article['titolo'].'</h5>
                        <div class="article-content" id="content-'.$article['id'].'" style="display: none; background-color: white; color: black;"></div>
                    </div>
                    <div class="card-footer py-3" style="background-color: #0066cc; color: white; padding: 8px; display: flex; justify-content: space-between;">
                        <span><i class="fa fa-calendar"></i> '.Carbon::parse($article['data'])->format('d/m/Y').'</span>
                        <span class="read-more">
                            <a href="javascript:void(0)" onclick="caricaArticolo('.$article['id'].')" style="color: white; text-decoration: none; font-weight: bold;" id="read-more-'.$article['id'].'">READ MORE</a>
                        </span>
                    </div>
                </div>';
            }
            echo '</div></div>';

            echo '<script>
                function caricaArticolo(id) {
                    var contentDiv = $("#content-" + id);
                    var readMoreBtn = $("#read-more-" + id);

                    // If content is already loaded and visible, hide it
                    if (contentDiv.is(":visible") && contentDiv.html() !== "") {
                        contentDiv.slideUp(300);
                        readMoreBtn.text("READ MORE");
                        return;
                    }

                    // If content is already loaded but hidden, show it
                    if (contentDiv.html() !== "") {
                        contentDiv.slideDown(300);
                        readMoreBtn.text("CLOSE");
                        return;
                    }

                    // Otherwise, load the content
                    localLoading(contentDiv, true);

                    $.get("'.base_path().'/ajax_complete.php?module=Messaggi&op=articolo&id=" + id, function(data) {
                        contentDiv.html(data);
                        contentDiv.slideDown(300);
                        readMoreBtn.text("CLOSE");
                        localLoading(contentDiv, false);
                    });
                }
            </script>';
        } else {
            echo '<div class="alert alert-info" style="background-color: transparent; color: white; border: 1px solid rgba(255, 255, 255, 0.3);">Non ci sono notizie disponibili al momento.</div>';
        }
        break;

    case 'articolo':
        $id = get('id');
        $article = $dbo->fetchOne("SELECT * FROM gdr_news WHERE id = ".prepare($id));

        if (!empty($article)) {

            // Corpo dell'articolo
            echo '<div style="background-color: white; color: black;">';

            // Format content - handle [excelsior-img] tags
            $content = $article['contenuto'];
            if (strpos($content, '[excelsior-img]') !== false) {
                $pattern = '/\[excelsior-img\](.*?)\[excelsior-img\]/s';
                $replacement = '<div style="text-align: center;"><img src="$1" style="max-width: 70%;" /></div>';
                $content = preg_replace($pattern, $replacement, $content);
            }

            echo '<br><hr>'.$content;
            echo '</div>';
        } else {
            echo '<div class="alert alert-danger" style="background-color: transparent; color: white; border: 1px solid rgba(255, 0, 0, 0.5);">Articolo non trovato.</div>';
        }

        break;
}